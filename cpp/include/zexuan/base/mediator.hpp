#ifndef ZEXUAN_BASE_MEDIATOR_H
#define ZEXUAN_BASE_MEDIATOR_H

#include <map>
#include <mutex>
#include <shared_mutex>
#include <unordered_map>

#include "base_types.hpp"
namespace zexuan {
  namespace base {

    // 前向声明
    class Executor;
    class Controller;

    /**
     * @brief 服务中介类
     *
     * 实现对"控制器"和"执行器"行为的封装，使得两者相互解耦并实现通信
     * 使用现代 C++ 特性实现线程安全
     */
    class Mediator {
    public:
      /**
       * @brief 构造函数
       */
      Mediator();

      /**
       * @brief 析构函数
       */
      ~Mediator() noexcept;

      // 禁用拷贝和移动
      Mediator(const Mediator&) = delete;
      Mediator& operator=(const Mediator&) = delete;
      Mediator(Mediator&&) = delete;
      Mediator& operator=(Mediator&&) = delete;

      /**
       * @brief 注册控制器
       * @param controller_id 控制器ID
       * @param controller 控制器对象智能指针
       * @return 操作结果
       */
      VoidResult EnrollController(ObjectId controller_id, std::shared_ptr<Controller> controller);

      /**
       * @brief 注销控制器
       * @param controller_id 控制器对象唯一标识
       * @return 操作结果
       */
      VoidResult CancelController(ObjectId controller_id) noexcept;

      /**
       * @brief 注册执行器
       * @param executor_id 执行器对象唯一标识
       * @param executor 执行器对象智能指针
       * @return 操作结果
       */
      VoidResult EnrollExecutor(ObjectId executor_id, std::shared_ptr<Executor> executor);

      /**
       * @brief 注销执行器
       * @param executor_id 执行器对象唯一标识
       * @return 操作结果
       */
      VoidResult CancelExecutor(ObjectId executor_id) noexcept;

      /**
       * @brief 向执行器发送通用消息
       * @param executor_id 执行器ID
       * @param msg 要发送的通用消息
       * @param source_id 消息源对象ID
       * @return 操作结果
       */
      VoidResult SendCommonMsgToExecutor(ObjectId executor_id, const CommonMessage& msg,
                                         ObjectId source_id);

      /**
       * @brief 向控制器发送通用消息
       * @param controller_id 控制器ID
       * @param msg 要发送的通用消息
       * @param source_id 消息源对象ID
       * @return 操作结果
       */
      VoidResult SendCommonMsgToController(ObjectId controller_id, const CommonMessage& msg,
                                           ObjectId source_id);

      /**
       * @brief 向控制器发送事件消息
       * @param msg 要发送的事件消息
       * @param source_id 消息源对象ID
       * @return 发送成功的控制器数量
       */
      Result<size_t> SendEventMsgToController(const EventMessage& msg, ObjectId source_id);

      /**
       * @brief 通过执行器关注的设备获取执行器的唯一标识
       * @param device_id 设备的标号
       * @param device_type 设备类型
       * @return 执行器唯一标识，失败返回错误
       */
      Result<ObjectId> GetExecutorIdByDevID(DeviceId device_id, DeviceCategory device_type) const;

      /**
       * @brief 获取统计信息
       * @return 包含注册对象数量等统计信息
       */
      struct Statistics {
        size_t controller_count{0};
        size_t executor_count{0};
        size_t device_mapping_count{0};
      };
      Statistics GetStatistics() const noexcept;

      /**
       * @brief 清理所有注册的对象（用于测试或重置）
       * @return 操作结果
       */
      VoidResult ClearAll() noexcept;

    private:
      /**
       * @brief 根据控制器ID查找控制器对象信息
       * @param controller_id 控制器ID
       * @return 对象智能指针
       */
      std::shared_ptr<Controller> GetControllerInfoByID(ObjectId controller_id) const;

      /**
       * @brief 根据执行器ID查找执行器对象信息
       * @param executor_id 执行器ID
       * @return 对象智能指针
       */
      std::shared_ptr<Executor> GetExecutorInfoByID(ObjectId executor_id) const;

      /**
       * @brief 根据设备ID查找执行器对象信息
       * @param device_id 设备ID
       * @return 对象智能指针
       */
      std::shared_ptr<Executor> GetExecutorInfoByDevID(const DeviceUUID& device_id) const;

      /**
       * @brief 添加执行器管理的设备映射关系
       * @param executor 执行器对象智能指针
       * @return 操作结果
       */
      VoidResult AddDevToExecutorMap(std::shared_ptr<Executor> executor);

      /**
       * @brief 移除执行器管理的设备映射关系
       * @param executor 执行器对象智能指针
       * @return 操作结果
       */
      VoidResult RemoveDevToExecutorMap(std::shared_ptr<Executor> executor) noexcept;

    private:
      /** @brief 单实例指针 */
      static std::shared_ptr<Mediator> instance_;

      /** @brief 单例创建互斥锁 */
      static std::once_flag instance_flag_;

      /** @brief 控制器映射表 */
      std::unordered_map<ObjectId, std::shared_ptr<Controller>> controller_map_;

      /** @brief 执行器映射表 */
      std::unordered_map<ObjectId, std::shared_ptr<Executor>> executor_map_;

      /** @brief 设备到执行器映射表 */
      std::map<DeviceUUID, std::shared_ptr<Executor>> dev_to_executor_map_;

      /** @brief 控制器映射表互斥锁 */
      mutable std::shared_mutex controller_mutex_;

      /** @brief 执行器映射表互斥锁 */
      mutable std::shared_mutex executor_mutex_;

      /** @brief 设备到执行器映射表互斥锁 */
      mutable std::shared_mutex dev_to_executor_mutex_;
    };

  }  // namespace base
}  // namespace zexuan

#endif  // ZEXUAN_BASE_MEDIATOR_H
