/**
 * @file executor.h
 * @brief 执行器类定义
 * <AUTHOR> from CNXSubject
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_EXECUTOR_H
#define ZEXUAN_BASE_EXECUTOR_H

#include "register_object.hpp"

namespace zexuan {
  namespace base {

    /**
     * @brief 执行器对象
     *
     * 响应控制器命令并返回对应结果，有事件后即时通知控制器
     * 使用现代 C++ 特性实现线程安全和异常安全
     * 重构后实现IDeviceAware接口，提供更好的设备管理功能
     */
    class Executor : public RegisterObject, public IDeviceAware {
    public:
      /**
       * @brief 构造函数
       * @param object_id 对象唯一标识
       * @param mediator 中介者实例
       */
      Executor(ObjectId object_id, std::shared_ptr<Mediator> mediator);

      /**
       * @brief 析构函数
       */
      ~Executor() noexcept override;

      /**
       * @brief 设置自身管理的设备 (实现IDeviceAware接口)
       * @param devices 设备列表，nullopt 表示不管理任何设备
       * @return 操作结果
       */
      VoidResult SetCareDevices(std::optional<DeviceList> devices) override;

      /**
       * @brief 判断是否关注指定设备 (实现IDeviceAware接口)
       * @param device_id 设备ID
       * @return true:关注 false:不关注
       */
      bool IsCareDevInfo(const DeviceUUID& device_id) const noexcept override;

      /**
       * @brief 设置自身管理的设备 (向后兼容的接口)
       * @param devices 设备列表
       * @return 操作结果
       */
      virtual VoidResult SetManagerDev(DeviceList devices);

      /**
       * @brief 推送命令，控制器有命令时通过服务中介接口触发调用
       * @param command_msg 命令信息
       * @param source_id 推送方(控制器)的唯一标识
       * @return 操作结果
       */
      virtual Result<void> PushCommand(const CommonMessage& command_msg, ObjectId source_id);

      /**
       * @brief 发送结果给控制器
       * @param result_msg 结果信息
       * @return 操作结果
       */
      virtual Result<void> SendResult(const CommonMessage& result_msg);

      /**
       * @brief 发送事件通知给订阅该信息的控制器
       * @param event_msg 通知信息
       * @return 操作结果
       */
      virtual Result<void> SendEventNotify(const EventMessage& event_msg);

      /**
       * @brief 设置收到控制器命令后的回调处理函数
       * @param callback_func 现代化回调函数
       * @return 操作结果
       */
      virtual VoidResult SetCmdCallback(CommonMessageHandler callback_func) noexcept;

      /**
       * @brief 判断对于参数指定的事件类型和设备是否关注
       * @param event_type 事件类型
       * @param device_id 设备ID
       * @return true:关注 false:不关注
       */
      bool IsCare(EventType event_type, const DeviceUUID& device_id) const noexcept override;

    protected:
      /**
       * @brief 派生类特定的注册逻辑 (重写基类钩子方法)
       * @return 操作结果
       */
      VoidResult DoRegister() override;

      /**
       * @brief 派生类特定的注销逻辑 (重写基类钩子方法)
       * @return 操作结果
       */
      VoidResult DoUnregister() noexcept override;

    private:
      /** @brief 命令处理回调函数 */
      std::optional<CommonMessageHandler> cmd_callback_;

      /** @brief 保护回调函数的互斥锁 */
      mutable std::mutex callback_mutex_;
    };

  }  // namespace base
}  // namespace zexuan

#endif  // ZEXUAN_BASE_EXECUTOR_H
