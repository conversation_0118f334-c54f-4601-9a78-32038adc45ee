# Spdlog 日志使用规范

## 1. 日志等级使用规范

### 1.1 CRITICAL（程序无法继续运行）
**使用场景：**
- 系统崩溃前的最后记录
- 无法恢复的致命错误
- 核心组件初始化完全失败

**示例：**
```cpp
spdlog::critical("Failed to initialize core component {}, system shutting down", component_name);
spdlog::critical("Out of memory, cannot allocate {} bytes", required_size);
spdlog::critical("Configuration file corrupted, cannot start application");
```

### 1.2 ERROR（功能失败但程序可继续）
**使用场景：**
- 组件启动/停止失败
- 网络连接失败
- 消息处理失败
- 配置文件读取失败
- 资源分配失败

**示例：**
```cpp
spdlog::error("{}: Failed to start component", GetComponentName());
spdlog::error("Failed to connect to server {}:{}, error: {}", host, port, error_msg);
spdlog::error("Message processing failed for type {}: {}", msg_type, error_detail);
spdlog::error("Failed to load config from {}: {}", config_path, error_reason);
spdlog::error("Thread creation failed: {}", thread_error);
```

### 1.3 WARN（异常情况但可以处理）
**使用场景：**
- 重试操作
- 连接中断但会自动重连
- 性能警告（超时、队列满等）
- 非关键功能失败
- 资源使用率过高

**示例：**
```cpp
spdlog::warn("Connection lost, attempting to reconnect (attempt {}/{})", retry_count, max_retries);
spdlog::warn("Message queue is {}% full, consider increasing capacity", usage_percent);
spdlog::warn("Thread {} did not stop within timeout {}ms", thread_id, timeout_ms);
spdlog::warn("Unknown message type {}, ignoring", msg_type);
spdlog::warn("Performance warning: operation took {}ms (expected <{}ms)", actual_time, expected_time);
```

### 1.4 INFO（重要的业务流程信息）
**使用场景：**
- 组件生命周期状态变化
- 连接建立/断开
- 重要业务操作完成
- 系统启动/关闭
- 配置加载成功

**示例：**
```cpp
spdlog::info("{}: Component started successfully", GetComponentName());
spdlog::info("{}: Component stopped successfully", GetComponentName());
spdlog::info("Connection established: {} -> {}", local_addr, remote_addr);
spdlog::info("Connection closed: {}", remote_addr);
spdlog::info("Configuration loaded from {}", config_path);
spdlog::info("Processing completed: {} messages in {}ms", msg_count, duration);
```

### 1.5 DEBUG（开发调试信息）
**使用场景：**
- 详细的执行流程
- 参数值记录
- 中间状态信息
- 算法执行步骤
- 数据转换过程

**示例：**
```cpp
spdlog::debug("{}: Loading config from {}", GetComponentName(), config_path);
spdlog::debug("Processing message type {}, size {} bytes", msg_type, msg_size);
spdlog::debug("Creating {} worker threads", thread_count);
spdlog::debug("State transition: {} -> {}", old_state, new_state);
spdlog::debug("Parsed {} configuration items", config_count);
```

### 1.6 TRACE（最详细的执行信息）
**使用场景：**
- 函数进入/退出
- 循环迭代信息
- 内存分配/释放
- 锁获取/释放
- 详细的数据流追踪

**示例：**
```cpp
spdlog::trace("Entering function {} with {} parameters", __func__, param_count);
spdlog::trace("Exiting function {}, result: {}", __func__, result);
spdlog::trace("Processing item {}/{}", current_index, total_count);
spdlog::trace("Allocated {} bytes at address {}", size, ptr);
spdlog::trace("Acquired lock {} in {}ms", lock_name, wait_time);
```

## 2. 组件生命周期日志规范

### 2.1 Start阶段日志
```cpp
bool ComponentName::Start() {
    spdlog::info("{}: Starting component", GetComponentName());

    // 配置加载
    spdlog::debug("{}: Loading configuration from {}", GetComponentName(), config_path_);
    if (!LoadConfig()) {
        spdlog::error("{}: Failed to load configuration", GetComponentName());
        return false;
    }
    spdlog::info("{}: Configuration loaded successfully", GetComponentName());

    // 资源初始化
    spdlog::debug("{}: Initializing resources", GetComponentName());
    if (!InitializeResources()) {
        spdlog::error("{}: Failed to initialize resources", GetComponentName());
        return false;
    }

    // 线程启动
    spdlog::debug("{}: Starting {} worker threads", GetComponentName(), thread_count_);
    if (!StartThreads()) {
        spdlog::error("{}: Failed to start worker threads", GetComponentName());
        return false;
    }

    spdlog::info("{}: Component started successfully", GetComponentName());
    return true;
}
```

### 2.2 Stop阶段日志
```cpp
bool ComponentName::Stop() {
    spdlog::info("{}: Stopping component", GetComponentName());

    // 停止接收新请求
    spdlog::debug("{}: Stopping request acceptance", GetComponentName());
    StopAcceptingRequests();

    // 等待现有任务完成
    spdlog::debug("{}: Waiting for {} active tasks to complete", GetComponentName(), active_task_count_);
    if (!WaitForTasksCompletion(timeout_ms_)) {
        spdlog::warn("{}: Some tasks did not complete within timeout", GetComponentName());
    }

    // 停止线程
    spdlog::debug("{}: Stopping {} worker threads", GetComponentName(), thread_count_);
    if (!StopThreads()) {
        spdlog::error("{}: Failed to stop all threads cleanly", GetComponentName());
        return false;
    }

    // 清理资源
    spdlog::debug("{}: Cleaning up resources", GetComponentName());
    CleanupResources();

    spdlog::info("{}: Component stopped successfully", GetComponentName());
    return true;
}
```

## 3. 网络通信日志规范

### 3.1 连接管理
```cpp
// 连接建立
spdlog::info("Connection established: {} -> {}", local_addr, remote_addr);
spdlog::debug("Connection {} created with fd {}", conn_id, socket_fd);

// 连接失败
spdlog::error("Failed to connect to {}:{}, error: {}", host, port, error_msg);
spdlog::warn("Connection attempt {}/{} failed, retrying in {}s", attempt, max_attempts, retry_delay);

// 连接断开
spdlog::info("Connection closed: {} (reason: {})", remote_addr, close_reason);
spdlog::warn("Connection {} lost unexpectedly", conn_id);
```

### 3.2 消息处理
```cpp
// 消息接收
spdlog::debug("Received {} bytes from {}", bytes_count, remote_addr);
spdlog::trace("Raw message data: {}", hex_dump(data));

// 消息发送
spdlog::debug("Sending {} bytes to {}", bytes_count, remote_addr);
spdlog::trace("Outgoing message: type={}, size={}", msg_type, msg_size);

// 消息处理错误
spdlog::error("Message validation failed: {}", validation_error);
spdlog::warn("Unknown message type {}, ignoring", msg_type);
```

## 4. 错误处理集成

### 4.1 替代异常抛出
```cpp
// 不推荐：抛出异常
if (error_condition) {
    throw std::runtime_error("Something went wrong");
}

// 推荐：记录错误并返回状态
if (error_condition) {
    spdlog::error("{}: Operation failed: {}", GetComponentName(), error_detail);
    return false;
}
```

### 4.2 替代bool返回值的详细信息
```cpp
// 不推荐：只返回bool
bool ProcessMessage(const Message& msg) {
    if (msg.empty()) {
        return false; // 无法知道具体原因
    }
    return true;
}

// 推荐：详细记录并返回bool
bool ProcessMessage(const Message& msg) {
    if (msg.empty()) {
        spdlog::error("{}: Cannot process empty message", GetComponentName());
        return false;
    }

    if (!ValidateMessage(msg)) {
        spdlog::error("{}: Message validation failed for type {}", GetComponentName(), msg.type);
        return false;
    }

    spdlog::debug("{}: Successfully processed message type {}", GetComponentName(), msg.type);
    return true;
}
```

### 7.2 配置文件示例
```json
{
  "spdlog": {
    "level": "debug",
    "pattern": "[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v",
    "max_file_size": 10485760,
    "max_files": 5,
    "flush_on_debug": true,
    "log_directory": "logs"
  }
}
```

---

## 8. spdlog使用规范

### 8.1 API使用方式
```cpp
// 正确的使用方式：直接使用全局spdlog API
spdlog::info("{}: Component started successfully", GetComponentName());
spdlog::error("{}: Failed to initialize", GetComponentName());
spdlog::debug("{}: Processing message type {}", GetComponentName(), msg_type);
spdlog::warn("{}: Connection lost, retrying", GetComponentName());

// 错误的使用方式（不要这样做）
// auto logger = spdlog::get("component_name");
// logger->info("message");
//
// std::shared_ptr<spdlog::logger> logger_;
// logger_->info("message");
```

### 8.2 初始化说明
- spdlog将在主程序中统一初始化和配置
- 所有组件直接使用全局API，无需获取logger实例
- 配置包括日志等级、格式、输出文件等都在主程序中设置

---

**重要提醒：**
1. 所有错误都必须使用spdlog记录，不允许静默失败
2. 日志等级必须严格按照规范使用，不得随意降级或升级
3. 组件名称必须在日志中明确标识
4. 直接使用spdlog::info()等全局API，不要获取logger实例
