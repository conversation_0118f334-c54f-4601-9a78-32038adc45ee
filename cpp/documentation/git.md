# Git 常用指令文档

## 1. 基础配置

### 全局配置
```bash
# 设置用户名和邮箱
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 查看配置
git config --list
git config user.name
git config user.email

# 设置默认编辑器
git config --global core.editor "vim"

# 设置默认分支名
git config --global init.defaultBranch main
```

### 本地配置
```bash
# 仅对当前仓库设置
git config user.name "Your Name"
git config user.email "<EMAIL>"
```

## 2. 仓库初始化和克隆

### 初始化仓库
```bash
# 在当前目录初始化git仓库
git init

# 在指定目录初始化git仓库
git init <directory-name>
```

### 克隆仓库
```bash
# 克隆远程仓库
git clone <repository-url>

# 克隆到指定目录
git clone <repository-url> <directory-name>

# 克隆指定分支
git clone -b <branch-name> <repository-url>

# 浅克隆（只克隆最近的提交）
git clone --depth 1 <repository-url>
```

## 3. 文件状态和暂存

### 查看状态
```bash
# 查看工作区状态
git status

# 简洁格式查看状态
git status -s

# 查看忽略的文件
git status --ignored
```

### 添加文件到暂存区
```bash
# 添加指定文件
git add <file-name>

# 添加多个文件
git add <file1> <file2> <file3>

# 添加所有文件
git add .
git add -A

# 添加所有已跟踪的文件
git add -u

# 交互式添加
git add -i

# 分块添加
git add -p
```

### 移除文件
```bash
# 从暂存区移除文件（保留工作区文件）
git reset <file-name>

# 从git跟踪中移除文件
git rm <file-name>

# 只从git跟踪中移除，保留本地文件
git rm --cached <file-name>

# 移除目录
git rm -r <directory-name>
```

## 4. 提交操作

### 基本提交
```bash
# 提交暂存区的文件
git commit -m "commit message"

# 提交所有已跟踪的修改文件（跳过暂存）
git commit -am "commit message"

# 空提交
git commit --allow-empty -m "empty commit"
```

### 修改提交
```bash
# 修改最后一次提交
git commit --amend -m "new commit message"

# 修改最后一次提交（不修改消息）
git commit --amend --no-edit

# 修改最后一次提交的作者
git commit --amend --author="New Author <<EMAIL>>"
```

## 5. 查看历史和差异

### 查看提交历史
```bash
# 查看提交历史
git log

# 简洁格式
git log --oneline

# 图形化显示分支
git log --graph --oneline --all

# 查看指定文件的历史
git log <file-name>

# 查看指定作者的提交
git log --author="author-name"

# 查看指定时间范围的提交
git log --since="2023-01-01" --until="2023-12-31"

# 查看最近n次提交
git log -n 5
```

### 查看差异
```bash
# 查看工作区与暂存区的差异
git diff

# 查看暂存区与最后一次提交的差异
git diff --cached

# 查看工作区与最后一次提交的差异
git diff HEAD

# 查看两个提交之间的差异
git diff <commit1> <commit2>

# 查看指定文件的差异
git diff <file-name>
```

## 6. 分支操作

### 分支管理
```bash
# 查看所有分支
git branch

# 查看所有分支（包括远程）
git branch -a

# 查看远程分支
git branch -r

# 创建新分支
git branch <branch-name>

# 创建并切换到新分支
git checkout -b <branch-name>
git switch -c <branch-name>

# 切换分支
git checkout <branch-name>
git switch <branch-name>

# 重命名分支
git branch -m <old-name> <new-name>

# 删除分支
git branch -d <branch-name>

# 强制删除分支
git branch -D <branch-name>
```

### 分支合并
```bash
# 合并指定分支到当前分支
git merge <branch-name>

# 不使用快进合并
git merge --no-ff <branch-name>

# 只进行快进合并
git merge --ff-only <branch-name>

# 合并时创建合并提交
git merge --no-ff -m "merge message" <branch-name>
```

## 7. 远程仓库操作

### 远程仓库管理
```bash
# 查看远程仓库
git remote

# 查看远程仓库详细信息
git remote -v

# 添加远程仓库
git remote add <name> <url>

# 修改远程仓库URL
git remote set-url <name> <new-url>

# 重命名远程仓库
git remote rename <old-name> <new-name>

# 删除远程仓库
git remote remove <name>
```

### 推送和拉取
```bash
# 推送到远程仓库
git push <remote> <branch>

# 推送当前分支到远程
git push

# 推送所有分支
git push --all

# 推送标签
git push --tags

# 强制推送（危险操作）
git push --force
git push -f

# 安全的强制推送
git push --force-with-lease

# 设置上游分支并推送
git push -u <remote> <branch>
```

```bash
# 拉取远程更新
git pull

# 拉取指定远程分支
git pull <remote> <branch>

# 使用rebase方式拉取
git pull --rebase

# 只获取远程更新，不合并
git fetch

# 获取所有远程分支
git fetch --all

# 获取并清理远程分支
git fetch --prune
```

## 8. 标签操作

### 标签管理
```bash
# 查看所有标签
git tag

# 查看标签详细信息
git show <tag-name>

# 创建轻量标签
git tag <tag-name>

# 创建附注标签
git tag -a <tag-name> -m "tag message"

# 为指定提交创建标签
git tag <tag-name> <commit-hash>

# 删除本地标签
git tag -d <tag-name>

# 删除远程标签
git push <remote> --delete <tag-name>

# 推送标签到远程
git push <remote> <tag-name>

# 推送所有标签
git push <remote> --tags
```

## 9. 撤销和重置操作

### 撤销修改
```bash
# 撤销工作区文件的修改
git checkout -- <file-name>
git restore <file-name>

# 撤销暂存区的文件
git reset HEAD <file-name>
git restore --staged <file-name>

# 撤销所有工作区修改
git checkout .
git restore .
```

### 重置操作
```bash
# 软重置（保留工作区和暂存区）
git reset --soft <commit-hash>

# 混合重置（保留工作区，清空暂存区）
git reset --mixed <commit-hash>
git reset <commit-hash>

# 硬重置（清空工作区和暂存区）
git reset --hard <commit-hash>

# 重置到上一个提交
git reset --hard HEAD~1

# 重置到远程分支状态
git reset --hard origin/<branch-name>
```

### 回退操作
```bash
# 创建新提交来撤销指定提交
git revert <commit-hash>

# 撤销多个提交
git revert <commit1>..<commit2>

# 撤销合并提交
git revert -m 1 <merge-commit-hash>
```

## 10. 变基操作

### 基本变基
```bash
# 将当前分支变基到指定分支
git rebase <branch-name>

# 交互式变基
git rebase -i <commit-hash>

# 继续变基
git rebase --continue

# 跳过当前提交
git rebase --skip

# 中止变基
git rebase --abort
```

### 变基选项
```bash
# 变基到远程分支
git rebase origin/<branch-name>

# 保留合并提交的变基
git rebase --preserve-merges <branch-name>

# 自动解决冲突（使用我们的版本）
git rebase -X ours <branch-name>

# 自动解决冲突（使用他们的版本）
git rebase -X theirs <branch-name>
```

## 11. 储藏操作

### 基本储藏
```bash
# 储藏当前工作区和暂存区
git stash

# 储藏时添加消息
git stash save "stash message"
git stash push -m "stash message"

# 查看储藏列表
git stash list

# 应用最新的储藏
git stash apply

# 应用指定的储藏
git stash apply stash@{n}

# 弹出最新的储藏（应用并删除）
git stash pop

# 删除最新的储藏
git stash drop

# 删除指定的储藏
git stash drop stash@{n}

# 清空所有储藏
git stash clear
```

### 高级储藏
```bash
# 储藏包括未跟踪的文件
git stash -u

# 储藏包括忽略的文件
git stash -a

# 只储藏暂存区的文件
git stash --staged

# 交互式储藏
git stash -p

# 查看储藏的内容
git stash show
git stash show -p stash@{n}
```

## 12. 冲突解决

### 冲突处理
```bash
# 查看冲突文件
git status

# 查看冲突详情
git diff

# 使用合并工具解决冲突
git mergetool

# 手动标记冲突已解决
git add <resolved-file>

# 继续合并
git commit

# 中止合并
git merge --abort
```

### 冲突解决策略
```bash
# 使用我们的版本解决冲突
git checkout --ours <file-name>

# 使用他们的版本解决冲突
git checkout --theirs <file-name>

# 查看冲突的不同版本
git show :1:<file-name>  # 共同祖先版本
git show :2:<file-name>  # 我们的版本
git show :3:<file-name>  # 他们的版本
```

## 13. 子模块操作

### 子模块管理
```bash
# 添加子模块
git submodule add <repository-url> <path>

# 初始化子模块
git submodule init

# 更新子模块
git submodule update

# 初始化并更新子模块
git submodule update --init

# 递归更新子模块
git submodule update --init --recursive

# 拉取子模块最新代码
git submodule update --remote

# 查看子模块状态
git submodule status

# 删除子模块
git submodule deinit <path>
git rm <path>
```

## 14. 高级操作

### 樱桃拣选
```bash
# 应用指定提交到当前分支
git cherry-pick <commit-hash>

# 应用多个提交
git cherry-pick <commit1> <commit2>

# 应用提交范围
git cherry-pick <start-commit>..<end-commit>

# 只应用提交，不自动提交
git cherry-pick -n <commit-hash>

# 继续樱桃拣选
git cherry-pick --continue

# 中止樱桃拣选
git cherry-pick --abort
```

### 二分查找
```bash
# 开始二分查找
git bisect start

# 标记当前提交为坏的
git bisect bad

# 标记指定提交为好的
git bisect good <commit-hash>

# 重置二分查找
git bisect reset

# 自动化二分查找
git bisect run <test-script>
```

### 清理操作
```bash
# 清理未跟踪的文件（预览）
git clean -n

# 清理未跟踪的文件
git clean -f

# 清理未跟踪的文件和目录
git clean -fd

# 交互式清理
git clean -i

# 清理忽略的文件
git clean -fX

# 清理所有未跟踪的文件
git clean -fxd
```

## 15. 查看和搜索

### 文件查看
```bash
# 查看指定提交的文件内容
git show <commit-hash>:<file-path>

# 查看文件的每一行最后修改信息
git blame <file-name>

# 查看文件的修改历史
git log -p <file-name>

# 查看文件在指定提交时的内容
git show <commit-hash>:<file-name>
```

### 搜索操作
```bash
# 在工作区搜索文本
git grep "search-text"

# 在指定提交中搜索
git grep "search-text" <commit-hash>

# 搜索提交消息
git log --grep="search-text"

# 搜索提交内容
git log -S "search-text"

# 搜索添加或删除的行
git log -G "search-pattern"
```

## 16. 配置和别名

### 常用别名设置
```bash
# 设置常用别名
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'
git config --global alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"
```

### 其他配置
```bash
# 设置换行符处理
git config --global core.autocrlf true    # Windows
git config --global core.autocrlf input   # Mac/Linux

# 设置文件权限
git config --global core.filemode false

# 设置大小写敏感
git config --global core.ignorecase false

# 设置默认推送行为
git config --global push.default simple
```

## 17. 常用工作流程

### 功能开发流程
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "Add new feature"

# 3. 推送到远程
git push -u origin feature/new-feature

# 4. 合并到主分支
git checkout main
git pull origin main
git merge feature/new-feature
git push origin main

# 5. 删除功能分支
git branch -d feature/new-feature
git push origin --delete feature/new-feature
```

### 紧急修复流程
```bash
# 1. 从主分支创建修复分支
git checkout main
git checkout -b hotfix/urgent-fix

# 2. 修复和提交
git add .
git commit -m "Fix urgent issue"

# 3. 合并到主分支
git checkout main
git merge hotfix/urgent-fix
git push origin main

# 4. 合并到开发分支
git checkout develop
git merge hotfix/urgent-fix
git push origin develop

# 5. 删除修复分支
git branch -d hotfix/urgent-fix
```

---

## 注意事项

1. **危险操作**：`git reset --hard`、`git push --force` 等命令会永久删除数据，使用前请确认
2. **分支保护**：在团队协作中，主分支通常受到保护，需要通过Pull Request进行合并
3. **提交消息**：使用清晰、描述性的提交消息，遵循团队的提交规范
4. **定期同步**：经常从远程仓库拉取最新代码，避免冲突积累
5. **备份重要工作**：在进行复杂操作前，确保重要工作已经提交或备份