#include "zexuan/protocol/service/protocol_service.hpp"

#include <spdlog/spdlog.h>
#include <chrono>
#include <fstream>
#include <thread>

#include "zexuan/protocol/transform/protocol_transform.hpp"

namespace zexuan {
  namespace protocol {
    namespace service {

      ProtocolService::ProtocolService(const std::string& config_file_path,
                                       std::shared_ptr<base::Mediator> mediator,
                                       base::ObjectId service_instance_id)
          : LifecycleComponentBase("ProtocolService"),
            mediator_(mediator), config_file_path_(config_file_path), service_instance_id_(service_instance_id) {
        spdlog::info("{}: Component created, ready to start", GetComponentName());
      }

      ProtocolService::~ProtocolService() {
        // 确保在析构前调用Stop()
        if (GetState() != base::ComponentState::STOPPED) {
          spdlog::warn("{}: Component not stopped before destruction, forcing stop", GetComponentName());
          Stop();
        }
        spdlog::info("{}: Component destroyed", GetComponentName());
      }

      bool ProtocolService::Start() {
        if (!TransitionTo(base::ComponentState::STARTING)) {
          return false;
        }

        try {
          spdlog::info("{}: Starting component", GetComponentName());

          // 1. 读取配置文件
          spdlog::debug("{}: Loading configuration from {}", GetComponentName(), config_file_path_);
          if (!LoadConfiguration()) {
            spdlog::error("{}: Failed to load configuration", GetComponentName());
            TransitionTo(base::ComponentState::ERROR);
            return false;
          }
          spdlog::info("{}: Configuration loaded successfully", GetComponentName());

          // 2. 创建Controller和Executor
          spdlog::debug("{}: Creating Controller and Executor", GetComponentName());
          if (!CreateControllerAndExecutor()) {
            spdlog::error("{}: Failed to create Controller and Executor", GetComponentName());
            TransitionTo(base::ComponentState::ERROR);
            return false;
          }

          // 3. 验证Mediator
          if (!mediator_) {
            spdlog::error("{}: Mediator is null", GetComponentName());
            TransitionTo(base::ComponentState::ERROR);
            return false;
          }

          // 4. 初始化Controller和Executor（注册到Mediator）
          spdlog::debug("{}: Initializing Controller and Executor", GetComponentName());
          auto controller_result = controller_->Init();
          if (!controller_result) {
            spdlog::error("{}: Failed to initialize controller: {}", GetComponentName(),
                          static_cast<int>(controller_result.error()));
            TransitionTo(base::ComponentState::ERROR);
            return false;
          }

          auto executor_result = executor_->Init();
          if (!executor_result) {
            spdlog::error("{}: Failed to initialize executor: {}", GetComponentName(),
                          static_cast<int>(executor_result.error()));
            TransitionTo(base::ComponentState::ERROR);
            return false;
          }

          // 5. 初始化总线客户端
          spdlog::debug("{}: Initializing bus client", GetComponentName());
          should_stop_ = false;
          if (!InitializeBusClient()) {
            spdlog::error("{}: Failed to initialize bus client", GetComponentName());
            TransitionTo(base::ComponentState::ERROR);
            return false;
          }

          if (!TransitionTo(base::ComponentState::RUNNING)) {
            return false;
          }

          spdlog::info("{}: Component started successfully", GetComponentName());
          return true;

        } catch (const std::exception& e) {
          spdlog::error("{}: Failed to start component: {}", GetComponentName(), e.what());
          TransitionTo(base::ComponentState::ERROR);
          return false;
        }
      }

      bool ProtocolService::Stop() {
        if (GetState() == base::ComponentState::STOPPED) {
          return true;  // 已经停止
        }

        if (!TransitionTo(base::ComponentState::STOPPING)) {
          return false;
        }

        spdlog::info("{}: Stopping component", GetComponentName());

        // 1. 停止接收新请求
        spdlog::debug("{}: Stopping request acceptance", GetComponentName());
        should_stop_ = true;

        // 2. 停止总线事件循环
        spdlog::debug("{}: Stopping bus event loop", GetComponentName());
        StopBusEventLoop();

        // 3. 从Mediator注销Controller和Executor
        spdlog::debug("{}: Unregistering from Mediator", GetComponentName());
        if (controller_) {
          auto result = controller_->Exit();
          if (!result) {
            spdlog::warn("{}: Failed to unregister controller: {}", GetComponentName(),
                        static_cast<int>(result.error()));
          }
        }

        if (executor_) {
          auto result = executor_->Exit();
          if (!result) {
            spdlog::warn("{}: Failed to unregister executor: {}", GetComponentName(),
                        static_cast<int>(result.error()));
          }
        }

        // 4. 清理资源
        spdlog::debug("{}: Cleaning up resources", GetComponentName());
        is_running_ = false;

        if (!TransitionTo(base::ComponentState::STOPPED)) {
          return false;
        }

        spdlog::info("{}: Component stopped successfully", GetComponentName());
        return true;
      }

      void ProtocolService::SetProtocolTransform(
          std::unique_ptr<transform::ProtocolTransform> transform) {
        protocol_transform_ = std::move(transform);
        spdlog::info("ProtocolService set protocol transform");
      }

      // 事件通过Subject的SendEventNotify方法发送，无需单独的回调设置方法

      // 消息处理回调
      void ProtocolService::OnCommonMessage(const base::CommonMessage& message) {
        spdlog::debug("Service received CommonMessage from {}, original target_id={}", message.source_id, message.target_id);

        // 直接转发到总线，让tcp_bus_client处理和生成响应
        if (bus_client_ && bus_client_->isConnected()) {
          // 创建转发消息，设置正确的source_id和target_id
          zexuan::base::CommonMessage forward_msg = message;
          forward_msg.source_id = service_instance_id_;  // 设置为自己的ID以便接收回复
          // 保持原始消息的target_id，这样能正确路由到目标tcp_bus_client
          spdlog::debug("ProtocolService: Before forwarding - target_id={}", forward_msg.target_id);

          if (bus_client_->sendCommonMessage(forward_msg)) {
            spdlog::debug("ProtocolService: Forwarded CommonMessage to bus for processing (target_id={})", forward_msg.target_id);
          } else {
            spdlog::error("ProtocolService: Failed to forward CommonMessage to bus");
          }
        } else {
          spdlog::error("ProtocolService: Bus client not connected, cannot forward message");
        }
      }

      void ProtocolService::OnEventMessage(const base::EventMessage& message) {
        spdlog::debug("Service received EventMessage");

        // 暂时空实现
        // TODO: 处理事件消息
      }

      // 业务处理线程已移除，消息直接转发到总线

      // 业务消息处理已移植到tcp_bus_client

      // 所有响应生成方法已移植到tcp_bus_client

      // 移除所有响应生成和队列管理方法


      // 事件上报功能已移植到tcp_bus_client

      // 总线相关方法实现
      bool ProtocolService::InitializeBusClient() {
        try {
          // 启动总线事件循环线程，在线程中创建EventLoop和TcpBusClient
          StartBusEventLoop();

          // 等待EventLoop和TcpBusClient创建完成
          std::this_thread::sleep_for(std::chrono::milliseconds(100));

          if (!bus_event_loop_ || !bus_client_) {
            spdlog::error("Failed to create bus EventLoop or TcpBusClient");
            return false;
          }

          // TcpBusClient 已经在线程中创建、设置了回调并连接到总线

          // 等待连接建立
          for (int i = 0; i < 50 && !bus_client_->isConnected(); ++i) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
          }

          if (bus_client_->isConnected()) {
            // 订阅所有消息类型和事件类型，并注册自己的client_id
            std::vector<int> message_types;
            std::vector<int> event_types;

            // 订阅所有CommonMessage类型 (假设类型1-10)
            for (int i = 1; i <= 10; ++i) {
              message_types.push_back(i);
            }

            // 订阅统一的事件类型1
            event_types.push_back(1);  // 统一的事件类型

            // 使用service_instance_id_作为client_id进行订阅，以便能够接收回复
            if (bus_client_->subscribe(message_types, event_types, service_instance_id_)) {
              spdlog::info("ProtocolService: Successfully subscribed to all message types on bus with client_id: {}", service_instance_id_);
              return true;
            } else {
              spdlog::error("ProtocolService: Failed to subscribe to message types on bus");
              return false;
            }
          } else {
            spdlog::error("ProtocolService: Failed to connect to message bus at {}:{}", bus_host_, bus_port_);
            return false;
          }

        } catch (const std::exception& e) {
          spdlog::error("ProtocolService: Exception initializing bus client: {}", e.what());
          return false;
        }
      }

      void ProtocolService::StartBusEventLoop() {
        bus_thread_ = std::thread([this]() {
          spdlog::info("ProtocolService: Bus event loop thread started");
          try {
            // 在线程中创建 EventLoop 和 TcpBusClient，确保它们在同一个线程中管理
            auto local_event_loop = std::make_shared<zexuan::net::EventLoop>();
            bus_event_loop_ = local_event_loop;  // 保存引用供外部查询

            // 在同一个线程中创建 TcpBusClient
            auto local_bus_client = std::make_shared<zexuan::bus::TcpBusClient>(
                config_file_path_, local_event_loop.get(), "ProtocolService");
            bus_client_ = local_bus_client;  // 保存引用供外部使用

            // 设置总线消息回调
            local_bus_client->setCommonMessageCallback(
                [this](const zexuan::base::CommonMessage& msg) { OnBusCommonMessage(msg); });
            local_bus_client->setEventMessageCallback(
                [this](const zexuan::base::EventMessage& msg) { OnBusEventMessage(msg); });
            local_bus_client->setControlMessageCallback(
                [this](const base::ControlMessage& msg) { OnBusControlMessage(msg); });

            // 启动总线客户端
            if (!local_bus_client->Start()) {
                spdlog::error("{}: Failed to start TcpBusClient", GetComponentName());
                return;
            }

            spdlog::debug("ProtocolService: Created EventLoop and TcpBusClient in bus thread");

            // 运行EventLoop
            local_event_loop->loop();

            // EventLoop 结束后，在同一个线程中清理所有对象
            // 确保正确的析构顺序：先显式停止 TcpBusClient，再析构对象
            spdlog::debug("ProtocolService: Cleaning up TcpBusClient and EventLoop in bus thread");

            // 显式停止TcpBusClient
            if (local_bus_client) {
              spdlog::debug("ProtocolService: Stopping TcpBusClient in bus thread");
              if (!local_bus_client->Stop()) {
                spdlog::warn("ProtocolService: Failed to stop TcpBusClient gracefully");
              }
            }

            bus_client_.reset();      // 清理外部引用
            local_bus_client.reset(); // 析构 TcpBusClient
            bus_event_loop_.reset();  // 清理外部引用
            // local_event_loop 会在函数结束时自动析构

          } catch (const std::exception& e) {
            spdlog::error("ProtocolService: Exception in bus event loop: {}", e.what());
          }
          spdlog::info("ProtocolService: Bus event loop thread stopped");
        });
      }

      void ProtocolService::StopBusEventLoop() {
        // 停止EventLoop，这会导致线程退出并自动清理所有对象
        if (bus_event_loop_) {
          bus_event_loop_->quit();
        }

        // 等待线程结束，此时所有对象都已经在正确的线程中析构
        if (bus_thread_.joinable()) {
          bus_thread_.join();
        }

        // 线程已经清理了所有引用，这里不需要再做任何操作
        // bus_client_ 和 bus_event_loop_ 已经在线程中被 reset
      }

      // 总线消息处理回调
      void ProtocolService::OnBusCommonMessage(const zexuan::base::CommonMessage& message) {
        spdlog::info("ProtocolService: Received CommonMessage from bus: type={}, source_id={}, invoke_id={}",
                    static_cast<int>(message.type), message.source_id, message.invoke_id);

        // 如果这是响应消息（来自tcp_bus_client），通过protocol_server发送出去
        if (message.type == zexuan::base::MessageType::RESULT && executor_) {
          // 通过Executor发送响应消息
          auto result = executor_->SendResult(message);
          if (result.has_value()) {
            spdlog::debug("ProtocolService: Sent response message through protocol_server");
          } else {
            spdlog::error("ProtocolService: Failed to send response message: error code {}", static_cast<int>(result.error()));
          }
        } else {
          spdlog::debug("ProtocolService: Ignoring non-response message from bus");
        }
      }

      void ProtocolService::OnBusEventMessage(const zexuan::base::EventMessage& message) {
        spdlog::info("ProtocolService: Received EventMessage from bus: event_type={}, source_id={}, description={}",
                    message.event_type, message.source_id, message.description);

        // 将从总线接收到的事件发送给gateway（通过protocol_server）
        if (executor_) {
          auto result = executor_->SendEventNotify(message);
          if (result.has_value()) {
            spdlog::debug("ProtocolService: Sent EventMessage to gateway through protocol_server");
          } else {
            spdlog::error("ProtocolService: Failed to send EventMessage to gateway: error code {}", static_cast<int>(result.error()));
          }
        } else {
          spdlog::error("ProtocolService: No Executor available, EventMessage not sent to gateway");
        }
      }

      void ProtocolService::OnBusControlMessage(const base::ControlMessage& message) {
        spdlog::info("ProtocolService: Received ControlMessage from bus: action={}, success={}, message={}",
                    message.action, message.success, message.message);
      }

      bool ProtocolService::LoadConfiguration() {
        std::ifstream config_file(config_file_path_);
        if (!config_file.is_open()) {
          spdlog::error("{}: Cannot open config file: {}", GetComponentName(), config_file_path_);
          return false;
        }

        nlohmann::json config;
        try {
          config_file >> config;
        } catch (const std::exception& e) {
          spdlog::error("{}: Failed to parse config file {}: {}", GetComponentName(), config_file_path_, e.what());
          return false;
        }

        // 读取服务配置
        if (!config.contains("protocol") || !config["protocol"].contains("service")) {
          spdlog::error("{}: Missing protocol.service section in config file: {}", GetComponentName(), config_file_path_);
          return false;
        }

        auto service_config = config["protocol"]["service"];

        // 读取必需的配置项，不允许默认值
        if (!service_config.contains("host")) {
          spdlog::error("{}: Missing required config: protocol.service.host", GetComponentName());
          return false;
        }
        if (!service_config.contains("port")) {
          spdlog::error("{}: Missing required config: protocol.service.port", GetComponentName());
          return false;
        }

        bus_host_ = service_config["host"];
        bus_port_ = service_config["port"];

        spdlog::debug("{}: Loaded configuration - bus: {}:{}",
                     GetComponentName(), bus_host_, bus_port_);
        return true;
      }

      bool ProtocolService::CreateControllerAndExecutor() {
        // 创建Controller - 使用固定ID接收来自Gateway的命令
        controller_ = std::make_shared<base::Controller>(base::SERVICE_OBSERVER_ID, mediator_);

        // 设置Controller的事件回调 - 处理来自Gateway的EventMessage
        controller_->SetEventCallback(
            [this](const base::EventMessage& message) -> base::Result<void> {
              OnEventMessage(message);
              return base::Result<void>{};
            });

        // 创建Executor - 使用固定ID向Gateway发送响应和事件
        executor_ = std::make_shared<base::Executor>(base::SERVICE_SUBJECT_ID, mediator_);

        // 设置Executor的命令回调 - 处理来自Gateway的命令
        executor_->SetCmdCallback([this](const base::CommonMessage& message) -> base::Result<void> {
          OnCommonMessage(message);
          return base::Result<void>{};
        });

        spdlog::debug("{}: Created Controller (ID: {}) and Executor (ID: {}), Service Instance ID: {}",
                     GetComponentName(), base::SERVICE_OBSERVER_ID, base::SERVICE_SUBJECT_ID, service_instance_id_);
        return true;
      }

    }  // namespace service
  }  // namespace protocol
}  // namespace zexuan