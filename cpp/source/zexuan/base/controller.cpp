/**
 * @file observer.cpp
 * @brief 观察者类实现 - 现代化版本
 * <AUTHOR> from CNXObserver
 * @date 2025-08-16
 */

#include "zexuan/base/controller.hpp"

#include <format>
#include <iostream>

#include "zexuan/base/mediator.hpp"
#include "zexuan/utils/invoke_id_utils.hpp"

namespace zexuan {
  namespace base {

    Controller::Controller(ObjectId object_id, std::shared_ptr<Mediator> mediator)
        : RegisterObject(object_id, "Controller") {
      // 设置基类的 Mediator
      SetMediator(mediator);

      // 默认设置关注的设备为空
      reg_obj_.devices = std::nullopt;

      // 默认设置关注的事件信息为空
      reg_obj_.event_types = std::nullopt;
    }

    Controller::~Controller() noexcept {
      // 基类析构函数会调用Exit()
    }

    VoidResult Controller::SetCareEventType(std::optional<EventTypeList> event_types) {
      try {
        std::lock_guard<std::mutex> lock(state_mutex_);
        reg_obj_.event_types = std::move(event_types);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    bool Controller::IsCareEventInfo(EventType event_type) const noexcept {
      return RegisterObject::IsCareEventInfo(event_type);
    }

    VoidResult Controller::SetCareDevices(std::optional<DeviceList> devices) {
      try {
        std::lock_guard<std::mutex> lock(state_mutex_);
        reg_obj_.devices = std::move(devices);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    bool Controller::IsCareDevInfo(const DeviceUUID& device_id) const noexcept {
      return RegisterObject::IsCareDevInfo(device_id);
    }

    VoidResult Controller::SetCareDev(std::optional<DeviceList> devices) {
      // 向后兼容的接口，转发到新的接口
      return SetCareDevices(std::move(devices));
    }

    Result<void> Controller::SendCommand(const CommonMessage& command_msg, ObjectId executor_id) {
      auto mediator = GetMediator();
      if (!mediator) {
        std::cerr << "SendCommand() 服务中介对象为空\n";
        return std::unexpected(ErrorCode::MEDIATOR_NOT_AVAILABLE);
      }

      ObjectId target_executor_id = executor_id;

      // 如果没有指定执行器ID，通过设备查找
      if (target_executor_id == INVALID_ID) {
        // 这里简化处理，实际应该根据命令中的设备信息查找执行器
        std::cerr << "SendCommand() 未指定执行器ID且无法自动查找\n";
        return std::unexpected(ErrorCode::INVALID_PARAMETER);
      }

      // 创建命令副本，自动设置 invoke_id 包含自己的 ID
      CommonMessage cmd_copy = command_msg;
      if (cmd_copy.invoke_id.empty()) {
        cmd_copy.invoke_id = utils::invoke_id::Generate(reg_obj_.object_id);
      } else {
        // 如果已有 invoke_id，检查是否包含自己的 ID
        ObjectId parsed_id = utils::invoke_id::ParseObjectId(cmd_copy.invoke_id);
        if (parsed_id != reg_obj_.object_id) {
          std::cerr << std::format(
              "SendCommand() invoke_id 中的对象ID({}) 与当前观察者ID({}) 不匹配\n", parsed_id,
              reg_obj_.object_id);
          return std::unexpected(ErrorCode::INVALID_PARAMETER);
        }
      }

      auto result
          = mediator->SendCommonMsgToExecutor(target_executor_id, cmd_copy, reg_obj_.object_id);

      if (!result) {
        std::cerr << std::format("SendCommand() 发送命令失败\n");
        return result;
      } else {
        std::cout << std::format("SendCommand() 发送命令成功，invoke_id={}\n", cmd_copy.invoke_id);
        return {};
      }
    }

    Result<void> Controller::ReplyResult(const CommonMessage& result_msg, ObjectId source_id) {
      std::lock_guard<std::mutex> lock(callback_mutex_);

      if (!result_callback_) {
        std::cerr << "ReplyResult() 结果回调函数未设置\n";
        return std::unexpected(ErrorCode::CALLBACK_NOT_SET);
      }

      std::cout << std::format("ReplyResult() 收到来自执行器(ID={})的结果\n", source_id);

      // 调用回调函数处理结果
      auto result = result_callback_.value()(result_msg);
      return result.transform([]() { return; });  // 转换为 Result<void>
    }

    Result<void> Controller::PushEventNotify(const EventMessage& event_msg, ObjectId source_id) {
      std::lock_guard<std::mutex> lock(callback_mutex_);

      if (!event_callback_) {
        std::cerr << "PushEventNotify() 事件回调函数未设置\n";
        return std::unexpected(ErrorCode::CALLBACK_NOT_SET);
      }

      std::cout << std::format("PushEventNotify() 收到来自执行器(ID={})的事件通知\n", source_id);

      // 调用回调函数处理事件
      auto result = event_callback_.value()(event_msg);
      return result.transform([]() { return; });  // 转换为 Result<void>
    }

    VoidResult Controller::SetResultCallback(CommonMessageHandler callback_func) noexcept {
      try {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        result_callback_ = std::move(callback_func);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult Controller::SetEventCallback(EventMessageHandler callback_func) noexcept {
      try {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        event_callback_ = std::move(callback_func);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    bool Controller::IsCare(EventType event_type, const DeviceUUID& device_id) const noexcept {
      // 控制器需要同时检查事件类型和设备
      bool care_event = IsCareEventInfo(event_type);
      bool care_device = IsCareDevInfo(device_id);

      // 如果设置了关注事件，则需要事件类型匹配
      // 如果设置了关注设备，则需要设备匹配
      if (reg_obj_.has_events() && reg_obj_.has_devices()) {
        return care_event && care_device;
      } else if (reg_obj_.has_events()) {
        return care_event;
      } else if (reg_obj_.has_devices()) {
        return care_device;
      }

      return false;
    }

    VoidResult Controller::DoRegister() {
      auto mediator = GetMediator();
      if (!mediator) {
        return std::unexpected(ErrorCode::MEDIATOR_NOT_AVAILABLE);
      }

      auto result = mediator->EnrollController(
          reg_obj_.object_id, std::static_pointer_cast<Controller>(shared_from_this()));

      if (!result) {
        std::cerr << std::format("DoRegister() 注册控制器到中介者失败\n");
        return result;
      }

      return {};
    }

    VoidResult Controller::DoUnregister() noexcept {
      try {
        auto mediator = GetMediator();
        if (!mediator) {
          return {};
        }

        auto result = mediator->CancelController(reg_obj_.object_id);

        if (!result) {
          std::cerr << std::format("DoUnregister() 从中介者注销控制器失败\n");
          return result;
        }

        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

  }  // namespace base
}  // namespace zexuan
