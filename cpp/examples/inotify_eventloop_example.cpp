/**
 * @file inotify_eventloop_example.cpp
 * @brief inotify与EventLoop集成示例
 * <AUTHOR>
 * @date 2025-08-27
 */

#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/channel.hpp"
#include <sys/inotify.h>
#include <unistd.h>
#include <spdlog/spdlog.h>
#include <iostream>
#include <string>
#include <unordered_map>
#include <memory>

using namespace zexuan::net;

class FileWatcher {
public:
    FileWatcher(EventLoop* loop) : loop_(loop) {
        // 创建inotify实例
        inotifyfd_ = ::inotify_init1(IN_NONBLOCK | IN_CLOEXEC);
        if (inotifyfd_ < 0) {
            spdlog::error("Failed to create inotify: {}", strerror(errno));
            throw std::runtime_error("inotify_init1 failed");
        }

        // 创建Channel并注册到EventLoop
        inotifyChannel_ = std::make_unique<Channel>(loop_, inotifyfd_);
        inotifyChannel_->setReadCallback([this](Timestamp) { handleRead(); });
        inotifyChannel_->enableReading();

        spdlog::info("FileWatcher created with inotify fd: {}", inotifyfd_);
    }

    ~FileWatcher() {
        if (inotifyChannel_) {
            inotifyChannel_->disableAll();
            inotifyChannel_->remove();
        }
        if (inotifyfd_ >= 0) {
            ::close(inotifyfd_);
        }
    }

    // 监听配置文件变化
    void watchConfigFile(const std::string& configPath) {
        int wd = ::inotify_add_watch(inotifyfd_, configPath.c_str(), 
                                     IN_MODIFY | IN_CLOSE_WRITE | IN_DELETE_SELF);
        if (wd < 0) {
            spdlog::error("Failed to watch config file {}: {}", configPath, strerror(errno));
            return;
        }
        
        watchDescriptors_[wd] = {configPath, WatchType::CONFIG_FILE};
        spdlog::info("Watching config file: {}", configPath);
    }

    // 监听日志目录
    void watchLogDirectory(const std::string& logDir) {
        int wd = ::inotify_add_watch(inotifyfd_, logDir.c_str(), 
                                     IN_CREATE | IN_DELETE | IN_MOVED_TO | IN_MOVED_FROM);
        if (wd < 0) {
            spdlog::error("Failed to watch log directory {}: {}", logDir, strerror(errno));
            return;
        }
        
        watchDescriptors_[wd] = {logDir, WatchType::LOG_DIRECTORY};
        spdlog::info("Watching log directory: {}", logDir);
    }

    // 监听临时文件创建
    void watchTempFileCreation(const std::string& tempDir) {
        int wd = ::inotify_add_watch(inotifyfd_, tempDir.c_str(), 
                                     IN_CREATE | IN_CLOSE_WRITE);
        if (wd < 0) {
            spdlog::error("Failed to watch temp directory {}: {}", tempDir, strerror(errno));
            return;
        }
        
        watchDescriptors_[wd] = {tempDir, WatchType::TEMP_DIRECTORY};
        spdlog::info("Watching temp directory for file creation: {}", tempDir);
    }

    // 设置回调函数
    void setConfigChangeCallback(std::function<void(const std::string&)> cb) {
        configChangeCallback_ = std::move(cb);
    }

    void setLogFileCallback(std::function<void(const std::string&, bool)> cb) {
        logFileCallback_ = std::move(cb); // bool: true=created, false=deleted
    }

    void setTempFileCallback(std::function<void(const std::string&)> cb) {
        tempFileCallback_ = std::move(cb);
    }

private:
    enum class WatchType {
        CONFIG_FILE,
        LOG_DIRECTORY,
        TEMP_DIRECTORY
    };

    struct WatchInfo {
        std::string path;
        WatchType type;
    };

    void handleRead() {
        char buffer[4096];
        ssize_t length = ::read(inotifyfd_, buffer, sizeof(buffer));
        
        if (length < 0) {
            if (errno != EAGAIN && errno != EWOULDBLOCK) {
                spdlog::error("Failed to read inotify events: {}", strerror(errno));
            }
            return;
        }

        char* ptr = buffer;
        while (ptr < buffer + length) {
            struct inotify_event* event = reinterpret_cast<struct inotify_event*>(ptr);
            processEvent(event);
            ptr += sizeof(struct inotify_event) + event->len;
        }
    }

    void processEvent(const struct inotify_event* event) {
        auto it = watchDescriptors_.find(event->wd);
        if (it == watchDescriptors_.end()) {
            spdlog::warn("Unknown watch descriptor: {}", event->wd);
            return;
        }

        const WatchInfo& watchInfo = it->second;
        std::string filename = event->len > 0 ? event->name : "";
        std::string fullPath = watchInfo.path;
        if (!filename.empty()) {
            fullPath += "/" + filename;
        }

        spdlog::info("File event: {} on {}", maskToString(event->mask), fullPath);

        // 根据监听类型处理事件
        switch (watchInfo.type) {
            case WatchType::CONFIG_FILE:
                handleConfigFileEvent(event, fullPath);
                break;
            case WatchType::LOG_DIRECTORY:
                handleLogDirectoryEvent(event, fullPath, filename);
                break;
            case WatchType::TEMP_DIRECTORY:
                handleTempDirectoryEvent(event, fullPath, filename);
                break;
        }
    }

    void handleConfigFileEvent(const struct inotify_event* event, const std::string& path) {
        if (event->mask & (IN_MODIFY | IN_CLOSE_WRITE)) {
            spdlog::info("Config file modified: {}", path);
            if (configChangeCallback_) {
                configChangeCallback_(path);
            }
        }
        
        if (event->mask & IN_DELETE_SELF) {
            spdlog::warn("Config file deleted: {}", path);
            // 可以选择重新监听或者通知应用程序
        }
    }

    void handleLogDirectoryEvent(const struct inotify_event* event, 
                                const std::string& fullPath, const std::string& filename) {
        if (event->mask & (IN_CREATE | IN_MOVED_TO)) {
            spdlog::info("Log file created: {}", fullPath);
            if (logFileCallback_) {
                logFileCallback_(fullPath, true);
            }
        }
        
        if (event->mask & (IN_DELETE | IN_MOVED_FROM)) {
            spdlog::info("Log file deleted: {}", fullPath);
            if (logFileCallback_) {
                logFileCallback_(fullPath, false);
            }
        }
    }

    void handleTempDirectoryEvent(const struct inotify_event* event, 
                                 const std::string& fullPath, const std::string& filename) {
        if (event->mask & IN_CREATE) {
            spdlog::info("Temp file created: {}", fullPath);
        }
        
        if (event->mask & IN_CLOSE_WRITE) {
            spdlog::info("Temp file write completed: {}", fullPath);
            if (tempFileCallback_) {
                tempFileCallback_(fullPath);
            }
        }
    }

    std::string maskToString(uint32_t mask) {
        std::vector<std::string> events;
        if (mask & IN_CREATE) events.push_back("CREATE");
        if (mask & IN_DELETE) events.push_back("DELETE");
        if (mask & IN_MODIFY) events.push_back("MODIFY");
        if (mask & IN_CLOSE_WRITE) events.push_back("CLOSE_WRITE");
        if (mask & IN_MOVED_FROM) events.push_back("MOVED_FROM");
        if (mask & IN_MOVED_TO) events.push_back("MOVED_TO");
        if (mask & IN_DELETE_SELF) events.push_back("DELETE_SELF");
        
        std::string result;
        for (size_t i = 0; i < events.size(); ++i) {
            if (i > 0) result += "|";
            result += events[i];
        }
        return result;
    }

private:
    EventLoop* loop_;
    int inotifyfd_;
    std::unique_ptr<Channel> inotifyChannel_;
    std::unordered_map<int, WatchInfo> watchDescriptors_;
    
    // 回调函数
    std::function<void(const std::string&)> configChangeCallback_;
    std::function<void(const std::string&, bool)> logFileCallback_;
    std::function<void(const std::string&)> tempFileCallback_;
};

// 使用示例
int main() {
    try {
        EventLoop loop;
        FileWatcher watcher(&loop);

        // 设置回调函数
        watcher.setConfigChangeCallback([](const std::string& path) {
            spdlog::info("应用程序: 配置文件 {} 已更改，重新加载配置", path);
            // 这里可以重新加载配置文件
        });

        watcher.setLogFileCallback([](const std::string& path, bool created) {
            if (created) {
                spdlog::info("应用程序: 新日志文件创建 {}", path);
                // 可以开始监控新的日志文件
            } else {
                spdlog::info("应用程序: 日志文件删除 {}", path);
                // 清理相关资源
            }
        });

        watcher.setTempFileCallback([](const std::string& path) {
            spdlog::info("应用程序: 临时文件写入完成 {}", path);
            // 处理临时文件
        });

        // 开始监听
        watcher.watchConfigFile("/tmp/app.conf");
        watcher.watchLogDirectory("/tmp/logs");
        watcher.watchTempFileCreation("/tmp/temp");

        spdlog::info("文件监听器已启动，可以尝试:");
        spdlog::info("  echo 'new config' > /tmp/app.conf");
        spdlog::info("  mkdir -p /tmp/logs && touch /tmp/logs/app.log");
        spdlog::info("  mkdir -p /tmp/temp && echo 'data' > /tmp/temp/temp_file.txt");

        // 启动事件循环
        loop.loop();

    } catch (const std::exception& e) {
        spdlog::error("Error: {}", e.what());
        return 1;
    }

    return 0;
}
