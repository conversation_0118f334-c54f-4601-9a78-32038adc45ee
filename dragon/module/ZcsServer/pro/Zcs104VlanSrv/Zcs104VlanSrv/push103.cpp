#include "push103.h"    
#include "SecDevFlowModule.h"
#include<sys/syscall.h>	
        
void* lock103_create()
{
	pthread_mutex_t* lock_key = (pthread_mutex_t*)::malloc(sizeof(pthread_mutex_t));
	int ret = pthread_mutex_init(lock_key,NULL);
	return (void*)lock_key;
	
}
void lock103_destroy(void* key)
{
	if(!key)return ;
	pthread_mutex_destroy((pthread_mutex_t*)key);
	::free(key);
}

CLIENT103::CLIENT103()
{
    nDevNo = -1;
}
CLIENT103::~CLIENT103()
{
    
}

push103::push103(CMessageLog* pFlowLog,bool *	pExit)
{
    m_pExit = pExit;
    m_pLogFile = pFlowLog;
    listen_fd = -1;
    lck_accept = lock103_create();
    
    lck_cur = lock103_create();//rcv

    // lmy add
    lck_IsDebug = lock103_create();
    m_bIsDebug = false;
   // lck_CloseUp = lock103_create();
   // lck_CloseDown = lock103_create();
    setUpCloseStn.clear();
    setDownCloseStn.clear();
    ThreadId = 0;
}
push103::~push103()
{
    lock103_destroy(lck_accept);
    lock103_destroy(lck_cur);
    lock103_destroy(lck_IsDebug);
    if(m_pLogFile!=NULL){
        delete m_pLogFile;
    }
}

int push103::create_listensock()
{
	int fd =-1;
	fd = socket(AF_INET,SOCK_STREAM,0);
	if( fd < 0)
		return -1;

	if( fcntl(fd, F_SETFL, fcntl(fd,F_GETFL)| O_NONBLOCK) < 0)
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::create_listensock]create_listensock: Can not set Non Block.",m_strStnId.c_str());
	unsigned long arg=1;
	if( ioctl(fd, FIONBIO, &arg) < 0){
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::create_listensock]create_listensock: Can not set Non Block.",m_strStnId.c_str());
	}
    unsigned optVal;
    socklen_t optLen = sizeof(unsigned int);
    getsockopt(fd, SOL_SOCKET, SO_SNDBUF, (char*)&optVal, &optLen);
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::create_listensock]fd Buffer length: %d",m_strStnId.c_str(), optVal);
    
	/////
	sockaddr_in addr;memset(&addr,0,sizeof(addr));addr.sin_family = AF_INET;
	addr.sin_port = htons(listen_port);
	if (!bindip.empty())
		addr.sin_addr.s_addr = inet_addr(bindip.c_str());
	if( bind( fd, (sockaddr*)&addr,sizeof(addr)) < 0){
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::create_listensock]create_listensock bind err<%s>, ip:%s ,port:%d",m_strStnId.c_str(),strerror(errno),bindip.c_str(),listen_port );
		closesocket(fd);
		return -1;
	}
	if( listen(fd, SOMAXCONN ) < 0){
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::create_listensock]create_listensock listen err<%s>",m_strStnId.c_str(),strerror(errno) );
		closesocket(fd);
		return -1;
	}
	return fd;
}

#define DELCLIENT(it)   { list<CLIENT103*>::iterator tmp; tmp = it; ++it; del( c ); \
                          busy.erase(tmp);   continue; }

int push103::select_loop()
{
	list<CLIENT103*> idle;//不用
	list<CLIENT103*> busy;
    
	int ufds_cnt = 1024 ;//最大epoll数
	pollfd* ufds = (pollfd*)malloc( sizeof(pollfd)*ufds_cnt );

	int accept_err = 0;
	lock103( lck_accept );
	if( listen_fd < 0) 
	while( (listen_fd =create_listensock() )< 0) {
            if(*m_pExit){
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]create_listensock 退出主循环  m_pExit[%d]",m_strStnId.c_str(),(*m_pExit));
                return 0;
            }
            sleep(5);
        }
	unlock103(lck_accept);

	while( !(*m_pExit) ){
	    int cur_cnt = busy.size() + 1 ;
	    if( cur_cnt > ufds_cnt) {
		ufds_cnt = busy.size() + 2048 ;
		ufds = (pollfd*)realloc( ufds, ufds_cnt* sizeof(pollfd) );
	    }
	    if( listen_fd < 0 ){
		sleep(30); 
            continue;
	    }
	    ufds[0].fd = listen_fd;
	    ufds[0].events = POLLIN; //102口的固定in
	    ufds[0].revents = 0;
        //准备非102的监视的文件符和方向
	   int i; list<CLIENT103*>::iterator it;
	   for( i=1, it = busy.begin() ; i<cur_cnt; ++i, ++it ){
			pollfd* u = &ufds[i]; //1~xx
            CLIENT103 * c = (*it);
			u->events |= POLLIN;
			u->events |= POLLOUT;
			u->revents = 0; 
			u->fd = c->sock;
		}

/* Poll the file descriptors described by the NFDS structures [starting] at
   FDS.  If TIMEOUT is nonzero and not -1, allow TIMEOUT milliseconds for
   an event to occur; if TIMEOUT is -1, block until an event occurs.
   Returns the number of file descriptors with events, zero if timed out,
   or -1 for errors.

   This function is a cancellation point and therefore not marked with
   __THROW.  */
	int status = poll( ufds, cur_cnt, 1000 );//start,maxnum,1000ms
	time_t cur = time(0);
	if( status < 0 ){
		sleep(30);
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::select_loop()]poll occur error <%s> .",m_strStnId.c_str(), strerror(errno) );
		continue;
	}
	else if( status == 0 ){//zero if timed out
            //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]------poll get timeout---status[%d][%02x]cur_cnt[%d]",m_strStnId.c_str(),status,ufds[0].revents,cur_cnt);
			//this->idle( &busy, &idle );
			continue;
		}
		//m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]------poll get ---status[%d][%02x]",m_strStnId.c_str(),status,ufds[0].revents);
        
        //先检查103口的fd-
	if(ufds[0].revents & POLLERR ){// accept error
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::select_loop()]accept error . ",m_strStnId.c_str());
		++accept_err ;
		if( accept_err > 500 ){
			accept_err =0;
			lock103( lck_accept);
			closesocket( listen_fd); listen_fd = -1;
			while( (listen_fd =create_listensock() )< 0)sleep(30 );
				unlock103(lck_accept);
			}
	}
        else if(ufds[0].revents & POLLIN ){
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]fd0 POLLIN ",m_strStnId.c_str());
			int s = -1;
			lock103( lck_accept);		
            //用于接受连接方的地址信息
            struct sockaddr_in clientaddr; 
            socklen_t clientlen = sizeof(clientaddr); //指定地址长度
			s = accept(ufds[0].fd, (struct sockaddr*)&clientaddr, &clientlen);
			unlock103(lck_accept);
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]  accept  add[%s]port[%d]",m_strStnId.c_str(), inet_ntoa(clientaddr.sin_addr),ntohs(clientaddr.sin_port));
            string strStnId;
            int n = getStnIdFromPort(inet_ntoa(clientaddr.sin_addr), ntohs(clientaddr.sin_port), strStnId);
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]getStnIdFromPort ret[%d]",m_strStnId.c_str(),n);
            if(n==0){
                if( s >= 0) {
                    if( add( s, &busy, &idle, clientaddr,strStnId) < 0 ) {
                        closesocket(s);
                    }
                }
                accept_err = 0;
            }else{
                closesocket(s);
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]getStnIdFromPort ret[%d] err 不socket",m_strStnId.c_str(),n);
            }
	}
	else
        {
		accept_err =0;
        }
        
        //检查所有监视
        //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]do all busy[%d]",m_strStnId.c_str(),cur_cnt-1);
	for( i=1, it = busy.begin() ; i<cur_cnt; ++i ){//不含Fd0[102端口的]
	    pollfd* u = &ufds[i];
            CLIENT103* c = (*it);
            if( (u->revents & POLLIN) || ( u->revents & POLLOUT) ){
                if(c!=NULL){
                    //m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::select_loop()]fd i[%d]c->type[%d],c=NULL,c->tmo[%p]",m_strStnId.c_str(),i,c->type,&c->tmo);
                    lock103(lck_cur);
                    c->tmo = cur;
                    unlock103(lck_cur);
                }else{
                    m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::select_loop()]fd i[%d]c->type[%d],c=NULL",m_strStnId.c_str(),i,c->type);
                }
            }
            //m_pLogFile->FormatAdd(CLogFile::trace,"i[%d]event[%02x]",i ,u->revents);
	    if( u->revents & POLLERR ){
                m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::select_loop()]fd i[%d]c->type[%d],POLLERR",m_strStnId.c_str(),i,c->type);
			DELCLIENT(it);
	     }
	     if( u->revents & POLLIN)
             {
               m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]fd i[%d]c->type[%d],POLLIN 读缓冲区可读",m_strStnId.c_str(),i,c->type);
	       if( rsock( c ) < 0)
               {
                    m_pLogFile->FormatAdd(CLogFile::error,"[%s] [chno %d ][push103::select_loop()]fd i[%d]c->type[%d],POLLIN rsock err del",m_strStnId.c_str(),c->nDevNo,i,c->type);
				DELCLIENT(it);
               }
             }
            if( u->revents & POLLOUT)
            {//send or  sendok
                if( wsock( c ) < 0)
                {
                    m_pLogFile->FormatAdd(CLogFile::error,"[%s][push103::select_loop()]fd i[%d]c->type[%x],POLLOUT wsock err del",m_strStnId.c_str(),i,c->type);
			DELCLIENT(it);
                }
            }
	    ++it;
	}
    
        MySleep(1);
	}
        
     m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::select_loop()]退出主循环  m_pExit[%d]",m_strStnId.c_str(),(*m_pExit));
    return 0;
}


void push103::idle( list<CLIENT103*>* busy, list<CLIENT103*>* idle )
{
	static time_t last_svr_chg = time(0);
	time_t cur = time(0);
	list<CLIENT103*>::iterator it, tmp;
	for( it = busy->begin(); it != busy->end(); ){
		///
	CLIENT103* c = (*it);
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::idle]  port[%d]  fd[%d] [%d]cur[%d]",m_strStnId.c_str(),c->addr.sin_port,c->sock,c->tmo,cur);
        lock103(lck_cur);
        int nd = abs(c->tmo - cur );
        unlock103(lck_cur);
	if( nd > 60 ){ //time out
	   tmp = it; 
            ++it; 
			//del( c );//超时暂时不断链
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::idle]  port[%d] >3600s close ",m_strStnId.c_str(),c->addr.sin_port);
			busy->erase(tmp);//不作为关注项
//			idle->push_back( c);            //  m_pLogFile->FormatAdd(CLogFile::trace,"DEL IDLE SOCKET");
			continue;
		}
		++it;
	}
}

int push103::add(int fd, list<CLIENT103*>* busy,list<CLIENT103*>* idle,sockaddr_in addr,string strStnId)
{
    int nRet=0;
   
    CLIENT103* c=0;
    c = new CLIENT103; if(!c) return -1;
    fcntl(fd, F_SETFL, fcntl(fd,F_GETFL)| O_NONBLOCK);
    unsigned long arg=1;
    ioctl(fd, FIONBIO, &arg);
    c->sock = fd;
    c->type = TP_LOGIN;
    c->tmo = time(0);
        //send
    c->curr_trans=0; 
    c->total_trans = BUF_SZ-1;
        //rcv
    c->curr_Rcv=0; 
    c->total_Rcv = BUF_SZ-1;
        //manager
    c->addr = addr;
    c->strStnId = strStnId.c_str();
    c->bFirstDtInit = true;
    
    c->nDevNo = 0;
    
    busy->push_back( c );
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::add]fd[%d]busy[%d]",m_strStnId.c_str(),fd,busy->size());
    
    return 0;

}
int push103::del(CLIENT103* c)
{
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::del]close socket fd[%d]",m_strStnId.c_str(),c->sock);
	if( (c->sock>=0)&&(c->sock<=65535) ){
		closesocket(c->sock);
		c->sock = -1;
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::del] socket连接断开 fd[%d] delete",m_strStnId.c_str(),c->sock);
        SecDevFlowMoudle::getInstanse()->setFntSta(c->strStnId,c->nDevNo,false);// 前置设备状态更新
        m_103Msg.removMsg(c->nDevNo);
        delete(c);
        c = NULL;
	}else{
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::del]close socket fd[%d]<0  not delete",m_strStnId.c_str(),c->sock); 
    }
    /*
    lock103(lck_CloseUp);
    setUpCloseStn.insert(m_strStnId.c_str());     
    unlock103(lck_CloseUp);*/
    return 0;
}
int push103::rsock(CLIENT103* c )
{
//        https://www.rfc-editor.org/rfc/rfc1006.txt
//byte    0               1             2             3                 4
//x10     0                   1                   2                   3
//bit     0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//       +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//       |      vrsn     |    reserved   |          packet length        |
//       +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//       packet length                16 bits (min=7, max=65535)
    
	switch(c->type)
	{
        default:
		{
            int r = 0;
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::rsock] [chno:%d]TP_LOGIN recv  head",m_strStnId.c_str(),c->nDevNo);
            while (c->curr_Rcv < RFC103_VERSION) {//4byte: to get all length
                r = recv (c->sock, c->rcvBuf + c->curr_Rcv, RFC103_HEAD_LEN - c->curr_Rcv, 0);
                if(r<=0) m_pLogFile->FormatAdd(CLogFile::trace,"rsock RFC103_VERSION :peer disconnected, negative if error %d",r);
                _CHECK103_BLOCK	//0 if peer disconnected, negative if error.
                c->curr_Rcv += r;	
            }
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::rsock] TP_LOGIN recv head OK ",m_strStnId.c_str());
            
	     // lmy modify
             if (c->rcvBuf[0] != RFC103_VERSION){
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::rsock]TP_END_CONNECTION TP0 TPDU too short to decode. TP_LOGIN Ignored.RFC1006_VERSION" ,m_strStnId.c_str());
                //c->type = TP_END_CONNECTION;
                return 0;
            }
            
            unsigned short tpkt_len=0;
            tpkt_len = ( ( (unsigned char) c->rcvBuf[2] ) << 8 ) | (unsigned char)c->rcvBuf[3];
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::rsock] TP_LOGIN 0x[%04x]=[%hu]",m_strStnId.c_str(),tpkt_len,tpkt_len);
            if ( tpkt_len == 0) {//unsigned short范围:0 ~ 65535 || tpkt_len > 65535 
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::rsock]TP_END_CONNECTION TP0 TPDU too short to decode.TP_LOGIN  Ignored.65535",m_strStnId.c_str() );
                //c->type = TP_END_CONNECTION;
                return 0;
            }
            
            if(tpkt_len > RFC103_HEAD_LEN)
            {
                while (c->curr_Rcv<tpkt_len)
                {
                   r = recv (c->sock, c->rcvBuf + c->curr_Rcv, tpkt_len - c->curr_Rcv, 0);
                   if(r<0)m_pLogFile->FormatAdd(CLogFile::trace,"RFC103_HEAD_LEN :peer disconnected, negative if error %d",r);
                    _CHECK103_BLOCK	//0 if peer disconnected, negative if error.
                   c->curr_Rcv += r;	
                }
            }
	    c->curr_Rcv = 0; 
	    c->total_Rcv = tpkt_len ;
            //c->total_Rcv = tpkt_len ;
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::rsock] read all TP_LOGIN [%hu]",m_strStnId.c_str(),tpkt_len);
            
            
	    if (c->rcvBuf[0] == RFC103_VERSION)
	    {
		  int nChNo =  (unsigned char)c->rcvBuf[1]; // 提取通道号
		  filter103Tpkt(c ,nChNo);
	    }
	}
	break;
    }
    return 0;
}
int push103::wsock(CLIENT103* c)
{
    // lmy add
    if (make103Tpkt(c)) return 0; 
}

//有需要立即返回的
int push103::wnonblock(CLIENT103* c)
{
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::wnonblock] wnonblock()[%d]",m_strStnId.c_str(),c->total_trans - c->curr_trans);
    
    if( c->curr_trans >= c->total_trans)
    {
	return 1;
    }
   
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s] [chno:%d] [push103::wnonblock] 向前置发送数据 len: %d ",m_strStnId.c_str(),c->nDevNo,c->total_trans - c->curr_trans);
    PrintSendBytes(c->buf,c->total_trans - c->curr_trans,c->nDevNo);

    int bb = c->total_trans - c->curr_trans;
    const int MAX_SEND = 4096;
    int cur = 0; 
    if(bb > MAX_SEND) return 2;
    int total = bb > MAX_SEND ? MAX_SEND : bb;
    while( cur < total )
    {
	int r = send(c->sock, c->buf+c->curr_trans, total - cur , 0);
        if(r<0) m_pLogFile->FormatAdd(CLogFile::trace,"send data : peer disconnected, negative if error %d",r);
	_CHECK103_BLOCK;
	c->curr_trans += r;
	cur  += r;
    }
    
    if( c->curr_trans < c->total_trans )
    {
	return -1;
    }
    
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::wnonblock] 向置发送数据成功 ,len %d ]",m_strStnId.c_str(),cur);
    
    return 0;
}
void* push103::_push_thr(void*_p)
{
	push103* p = (push103*)_p;
        
        p->m_pLogFile->FormatAdd(CLogFile::error,"[push103::Start()] _push_thr 线程id %d",syscall(SYS_gettid));
	p->select_loop();
   
	return 0;
}

int push103::start(string bindip,int port,string strStnId,map<string,pthread_t > &mapThreadIds)
{
    this->bindip = bindip.c_str();
	this->listen_port = port;
    this->m_strStnId = strStnId.c_str();
    
    //_push_thr
    int nRet = pthread_create(&ThreadId,NULL, _push_thr, this);
    if( nRet != 0 ){
        m_pLogFile->FormatAdd(CLogFile::error,"[push103::Start()] _push_thr 线程 启动时失败 原因:%s(%d)。",strerror(errno),errno);
        return -1;
	}else{
        m_pLogFile->FormatAdd(CLogFile::trace,"[push103::Start()] _push_thr 线程ID[%ld] 启动成功。",ThreadId);
	}
    //pthread_detach(ThreadId);
    mapThreadIds.insert(make_pair("_push_thr",ThreadId));
   
     return 0;
}

int push103::getStnIdFromPort(char * cIP, int nPort, string &strStnId)
{
    strStnId = m_strStnId.c_str();
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::getStnIdFromPort]  result '%s' ok!",m_strStnId.c_str(),strStnId.c_str());
    return 0;
}

int push103::PrintRecvBytes(const string &str,int nChNo)
{
    if(!isDebug()) return 0;
    vector<BYTE> vBytes;
    vBytes.insert(vBytes.end(),str.begin(),str.end());
    string str1;str1.clear();
    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
        char c20[20];
        snprintf(c20,20,"%02x ",(unsigned char)*it);
        str1=str1+c20;
    }

    m_pLogFile->FormatAdd(CLogFile::trace,"[push103::PrintRecvBytes] [stn: %s] [chno:%d ] len:%d, dic: %s",m_strStnId.c_str(),nChNo,str.length(),str1.c_str());
    return 0;
}

int push103::PrintSendBytes(char *message, const int message_len, int nChNo)
{
    if(!isDebug())return 0;
    string str1;
    str1.clear();
    for (int i = 0; i < message_len; i++) {
        char c20[20];
        snprintf(c20, 20, "%02x ", (unsigned char) message[i]);
        str1 = str1 + c20;
    }
    m_pLogFile->FormatAdd(CLogFile::trace, "[push103::PrintSendBytes] [stn: %s] [chno:%d ] len:%d, dic: %s", m_strStnId.c_str(), nChNo, message_len, str1.c_str());
    return 0;
}

// lmy add
int push103::filter103Tpkt(CLIENT103* c,int nChNo)
{
	m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::filter103Tpkt] ,ChNo : %d ",m_strStnId.c_str(),nChNo);
	char * pdu_ptr = c->rcvBuf;
	int pdu_len = c->total_Rcv;
        
        c->nDevNo = nChNo; // 赋值设备号

	pdu_len -= RFC103_HEAD_LEN;//Strip off TPKT header. 
	pdu_ptr += RFC103_HEAD_LEN;

	std::string strTpkt;
	strTpkt.assign(pdu_ptr,pdu_len);
	PrintRecvBytes(strTpkt,c->nDevNo);
	bzero(c->rcvBuf,sizeof(c->rcvBuf));
       
	// 接收前置过来数据 放入待发缓存管理模块
	if(pdu_len>0)
        {
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::filter103Tpkt] ,ChNo : %d  收到来自前置数据 数据长度 %d ", m_strStnId.c_str(),nChNo,pdu_len);
            SecDevFlowMoudle::getInstanse()->addSendMsg(c->strStnId,nChNo,strTpkt);
        }
        else if(pdu_len==0) 
        {
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103 No : %d ,前置注册连接,c->nDevNo:%d",m_strStnId.c_str(),nChNo,c->nDevNo); 
            int nState = ( SecDevFlowMoudle::getInstanse()->getDevStnSta(c->strStnId,nChNo) == CONNECT_CLOSE)? 0:1;
            notifyFrontDevState(nChNo,nState);
            m_103Msg.removMsg(nChNo); // 连接注册前先清除待发缓存
            SecDevFlowMoudle::getInstanse()->setFntSta(c->strStnId,nChNo,true);
        }
        else
        {
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push103::filter103Tpkt] ,ChNo : %d  接收前置数据失败， 数据长度 %d ", m_strStnId.c_str(),nChNo,pdu_len);
        }
}

bool push103::send103ToFront(std::string &str,int nNo)
{
    m_103Msg.addMsg(str,nNo);
    //m_pLogFile->FormatAdd(CLogFile::trace,"[push103] [%s：] No : %d, addMsg pre send, length :%d ",m_strStnId.c_str(),nNo,str.length());
}

bool push103::make103Tpkt(CLIENT103* c)
{
    int nMsgLen = m_103Msg.getTpktPack(c->buf,c->nDevNo);
    if(nMsgLen>0)
    {
        c->curr_trans = 0;
	c->total_trans = nMsgLen;
        int ret = wnonblock(c);
        if(nMsgLen==4)m_pLogFile->FormatAdd(CLogFile::trace,"[push103] [%s：] No : %d, 向前置发送站端设备状态包 ",m_strStnId.c_str(),c->nDevNo); 
	return  (ret ==0 ? true:false);
    }
  
    return false;
    
   
}

// nState 0: 未连接，1 连接
void push103::notifyFrontDevState(int nChNo,int nState) 
{
    m_103Msg.addStat(nChNo,nState);
    m_pLogFile->FormatAdd(CLogFile::trace, "[push103] [%s：] No : %d ,addStat 收到更新前置设备状态通知，状态 %d ", m_strStnId.c_str(), nChNo, nState);
}

void push103::setIsDebug(bool isDebug)
{
    lock103( lck_accept );
    m_bIsDebug = isDebug;
    unlock103(lck_accept);
}

bool push103::isDebug()
{
    bool bRet = false;
    lock103( lck_accept );
    bRet = m_bIsDebug;
    unlock103(lck_accept);
    
    return bRet;
}


